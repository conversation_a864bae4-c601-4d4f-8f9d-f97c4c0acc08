{"name": "flux-chat-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fal-ai/serverless-client": "^0.14.3", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "next": "15.3.3", "node-fetch": "^3.3.2", "openai": "^5.1.1", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}