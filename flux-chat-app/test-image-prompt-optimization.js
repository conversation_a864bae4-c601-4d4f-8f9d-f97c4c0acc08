// 测试图片场景下的DeepSeek提示词优化功能
// 使用Node.js 18+内置的fetch

const baseUrl = 'http://localhost:3000';

async function testImagePromptOptimization() {
  console.log('🧪 测试图片场景下的DeepSeek提示词优化');
  console.log('==================================================');

  try {
    // 模拟图片上传场景的提示词优化
    console.log('\n📸 测试1: 图片上传 + 文字描述的提示词优化');
    
    const response1 = await fetch(`${baseUrl}/api/optimize-prompt`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        requirement: '报人像',
        conversationHistory: [],
        useAI: true, // 使用DeepSeek
        image: {
          data: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
          mimeType: 'image/jpeg'
        }
      })
    });

    if (response1.ok) {
      const data1 = await response1.json();
      console.log('✅ 图片场景提示词优化成功');
      console.log('📝 原始提示:', data1.optimization.originalPrompt);
      console.log('🚀 优化提示:', data1.optimization.optimizedPrompt.substring(0, 100) + '...');
      console.log('💡 改进点数量:', data1.optimization.improvements.length);
      console.log('📊 信心度:', data1.optimization.confidence);
      
      // 检查是否包含DeepSeek优化标识
      const hasDeepSeekOptimization = data1.optimization.improvements.some(imp => 
        imp.includes('DeepSeek') || imp.includes('deepseek')
      );
      console.log('🤖 DeepSeek优化:', hasDeepSeekOptimization ? '✅ 是' : '❌ 否');
    } else {
      console.log('❌ 图片场景提示词优化失败:', response1.status);
      const errorData = await response1.text();
      console.log('错误信息:', errorData);
    }

    // 测试纯图片上传（无文字描述）
    console.log('\n📷 测试2: 纯图片上传（无文字描述）');
    
    const response2 = await fetch(`${baseUrl}/api/optimize-prompt`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        requirement: '', // 空的文字描述
        conversationHistory: [],
        useAI: true,
        image: {
          data: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
          mimeType: 'image/jpeg'
        }
      })
    });

    if (response2.ok) {
      const data2 = await response2.json();
      console.log('✅ 纯图片优化成功');
      console.log('🚀 优化提示:', data2.optimization.optimizedPrompt.substring(0, 100) + '...');
      console.log('💡 改进点数量:', data2.optimization.improvements.length);
      console.log('📊 信心度:', data2.optimization.confidence);
      
      const hasDeepSeekOptimization = data2.optimization.improvements.some(imp => 
        imp.includes('DeepSeek') || imp.includes('deepseek')
      );
      console.log('🤖 DeepSeek优化:', hasDeepSeekOptimization ? '✅ 是' : '❌ 否');
    } else {
      console.log('❌ 纯图片优化失败:', response2.status);
    }

    // 测试对话API在图片场景下的表现
    console.log('\n💬 测试3: 图片场景下的对话API');
    
    const response3 = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: '让这个人物更加卡通化',
        conversationHistory: [],
        image: {
          data: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
          mimeType: 'image/jpeg'
        }
      })
    });

    if (response3.ok) {
      const data3 = await response3.json();
      console.log('✅ 图片对话成功');
      console.log('💬 AI回复长度:', data3.response.length, '字符');
      console.log('🔍 需要澄清:', data3.needsClarification ? '是' : '否');
      
      // 检查回复是否提到了图片
      const mentionsImage = data3.response.includes('图片') || data3.response.includes('上传');
      console.log('📸 提到图片:', mentionsImage ? '✅ 是' : '❌ 否');
    } else {
      console.log('❌ 图片对话失败:', response3.status);
    }

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  }

  console.log('\n🎉 测试完成！');
  console.log('💡 如果看到"DeepSeek优化: ✅ 是"，说明修复成功');
}

testImagePromptOptimization().catch(console.error);
